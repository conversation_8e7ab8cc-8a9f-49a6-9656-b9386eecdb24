/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "side_panel_node_widget.h"
#include "side_panel_node_model.h"
#include "side_panel_node_view.h"

#include <QDebug>
#include <QHBoxLayout>
#include <QSortFilterProxyModel>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

SidePanelWidget::SidePanelWidget(QWidget* _parent) :
  QWidget(_parent),
  model_(new SidePanelNodeModel(this)),
  view_(new SidePanelNodeView(this)),
  search_edit_(new QLineEdit(this)),
  clear_button_(new QPushButton("×", this)),
  layout_(new QVBoxLayout(this))
{
  // 设置搜索框
  search_edit_->setPlaceholderText("搜索节点...");
  search_edit_->setClearButtonEnabled(true);
  search_edit_->setAlignment(Qt::AlignCenter);

  // 设置清除按钮
  clear_button_->setFixedSize(20, 20);
  clear_button_->setFlat(true);
  clear_button_->setVisible(false);

  // 创建搜索布局
  auto* search_layout = new QHBoxLayout();
  search_layout->addWidget(search_edit_);
  search_layout->addWidget(clear_button_);
  search_layout->setContentsMargins(0, 0, 0, 0);

  // 设置视图
  view_->setNodePaletteModel(model_);

  // 设置主布局
  layout_->addLayout(search_layout);
  layout_->addWidget(view_);
  layout_->setContentsMargins(0, 0, 0, 0);
  setLayout(layout_);

  // 连接信号和槽
  connect(search_edit_, &QLineEdit::textChanged, this, &SidePanelWidget::slotSearchTextChanged);
  connect(clear_button_, &QPushButton::clicked, this, &SidePanelWidget::slotClearSearchClicked);

  // 设置最小宽度
  setMinimumWidth(200);
}

void SidePanelWidget::initializeBasicNodes()
{
  // 添加控制节点
  addTitle("-- 根节点 (Root)");
  addNodeItem("开始 (Root)", "开始", "Subtree");

  // 添加控制节点
  addTitle("-- 控制节点 (Control)");
  addNodeItem("顺序 (Sequence)", "Sequence", "Control");
  addNodeItem("异步顺序 (AsyncSequence)", "AsyncSequence", "Control");
  addNodeItem("后备 (Fallback)", "Fallback", "Control");
  addNodeItem("异步后备 (AsyncFallback)", "AsyncFallback", "Control");
  addNodeItem("并行 (Parallel)", "Parallel", "Control");
  addNodeItem("并行所有 (ParallelAll)", "ParallelAll", "Control");

  // 添加装饰节点
  addTitle("-- 装饰节点 (Decorator)");
  addNodeItem("反转 (Inverter)", "Inverter", "Decorator");
  addNodeItem("重试直到成功 (RetryUntilSuccessful)", "RetryUntilSuccessful", "Decorator");
  addNodeItem("保持执行直到失败 (KeepRunningUntilFailure)", "KeepRunningUntilFailure", "Decorator");
  addNodeItem("重复 (Repeat)", "Repeat", "Decorator");
  addNodeItem("超时ms (Timeout)", "Timeout", "Decorator");
  addNodeItem("延时ms (Delay)", "Delay", "Decorator");
  addNodeItem("执行一次 (RunOnce)", "RunOnce", "Decorator");
  addNodeItem("强制成功 (ForceSuccess)", "ForceSuccess", "Decorator");
  addNodeItem("强制失败 (ForceFailure)", "ForceFailure", "Decorator");

  // 添加动作节点
  addTitle("-- 动作节点 (Action)");
  addNodeItem("延时ms (Sleep)", "Sleep", "Action");
  addNodeItem("设置黑板变量 (SetBlackboard)", "SetBlackboard", "Action");
  addNodeItem("删除黑板变量 (UnsetBlackboard)", "UnsetBlackboard", "Action");
  addNodeItem("打印变量 (PrintVariable)", "PrintVariable", "Action");

  // 添加条件节点
  addTitle("-- 条件节点 (Condition)");
  addNodeItem("脚本条件 (ScriptCondition)", "ScriptCondition", "Condition");

  // 添加子树节点
  addTitle("-- 子树节点 (SubTree)");
  addNodeItem("子树 (SubTree)", "SubTree", "Subtree");

  // 确保所有节点默认都是展开的
  view_->expandAll();
}

void SidePanelWidget::slotAddPluginNodes(const robosense::lidar::NodeInfos& _plugin_nodes)
{
  model_->addPluginNodes(_plugin_nodes);

  // 确保插件节点也默认展开
  view_->expandAll();
}

void SidePanelWidget::setMimeType(const QString& _mime_type) { model_->setMimeType(_mime_type); }

SidePanelNodeModel* SidePanelWidget::model() const { return model_; }

SidePanelNodeView* SidePanelWidget::view() const { return view_; }

void SidePanelWidget::addTitle(const QString& _title) { model_->addTitle(_title); }

void SidePanelWidget::addNodeItem(const std::string& _display_name,
                                  const std::string& _registered_model_name,
                                  const std::string& _category)
{
  model_->addNodeItem(_display_name, _registered_model_name, _category);
}

bool SidePanelWidget::removeItem(int _index) { return model_->removeItem(_index); }

int SidePanelWidget::removeItemsByCategory(const std::string& _category)
{
  return model_->removeItemsByCategory(_category);
}

bool SidePanelWidget::removeItemByRegisteredName(const std::string& _registered_model_name)
{
  return model_->removeItemByRegisteredName(_registered_model_name);
}

void SidePanelWidget::clear() { model_->clear(); }

void SidePanelWidget::filterNodes(const QString& _filter)
{
  // 创建一个临时的过滤代理模型
  auto* proxy_model = new QSortFilterProxyModel(this);
  proxy_model->setSourceModel(model_);
  proxy_model->setFilterCaseSensitivity(Qt::CaseInsensitive);
  proxy_model->setFilterRole(Qt::DisplayRole);
  proxy_model->setFilterFixedString(_filter);

  // 设置递归过滤，使得父项目在子项目匹配时也会显示
  proxy_model->setRecursiveFilteringEnabled(true);

  // 设置视图使用过滤后的模型
  view_->setModel(proxy_model);

  // 自动展开所有项目
  view_->expandAll();

  // 如果过滤文本为空，恢复使用原始模型
  if (_filter.isEmpty())
  {
    view_->setNodePaletteModel(model_);
    delete proxy_model;
  }
}

void SidePanelWidget::slotSearchTextChanged(const QString& _text)
{
  // 显示或隐藏清除按钮
  clear_button_->setVisible(!_text.isEmpty());

  // 过滤节点
  filterNodes(_text);
}

void SidePanelWidget::slotClearSearchClicked()
{
  // 清除搜索框
  search_edit_->clear();
}

}  // namespace robosense::lidar
