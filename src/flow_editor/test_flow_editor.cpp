/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "test_flow_editor.h"
#include "QtNodes/internal/Definitions.hpp"
#include "QtNodes/internal/NodeGraphicsObject.hpp"
#include "behaviortree_cpp/basic_types.h"
#include "common_types.h"
#include "data_serializer.h"
#include "node_models/bt_node_model.h"
#include "node_models/node_manager.h"
#include "node_utils.h"
#include "qt_logger.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "side_panel/side_panel_node_model.h"
#include "side_panel/side_panel_node_view.h"
#include "side_panel/side_panel_node_widget.h"
#include <QDrag>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMouseEvent>
#include <QtNodes/AbstractGraphModel>
#include <QtWidgets>
#include <memory>
#include <qchar.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

FlowScene::FlowScene(QtNodes::DataFlowGraphModel& _model, QObject* _parent) :
  QtNodes::DataFlowGraphicsScene(_model, _parent)
{
  QObject::connect(&_model, &QtNodes::DataFlowGraphModel::nodeCreated, this, &FlowScene::slotOnNodeCreate);
  QObject::connect(this, &QtNodes::DataFlowGraphicsScene::nodeDoubleClicked, this, &FlowScene::slotOnNodeDoubleClicked);
  QObject::connect(this, &QtNodes::DataFlowGraphicsScene::nodeContextMenu, this, &FlowScene::slotNodeContextMenu);
}

FlowScene::~FlowScene() = default;

void FlowScene::setEditLocked(bool _locked)
{
  is_edit_locked_ = _locked;

  LOG_INFO("FlowScene 编辑锁定状态: {}", is_edit_locked_ ? "已锁定" : "已解锁");
}

bool FlowScene::isEditLocked() const { return is_edit_locked_; }

void FlowScene::slotOnNodeDoubleClicked(QtNodes::NodeId _node_id)
{
  LOG_DEBUG("double clicked, node id : {}", _node_id);

  // 检查编辑锁定状态
  if (is_edit_locked_)
  {
    LOG_DEBUG("slotOnNodeDoubleClicked: 编辑已锁定，无法打开属性面板");
    return;
  }

  auto& model_ptr        = graphModel();
  auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
  if (data_graph_model == nullptr)
  {
    LOG_ERROR("slotOnNodeDoubleClicked : Failed to cast to DataFlowGraphModel");
    return;
  }
  auto* node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
  if (node_model == nullptr)
  {
    LOG_ERROR("slotOnNodeDoubleClicked : Failed to cast to BtNodeModel");
    return;
  }

  QtNodes::NodeGraphicsObject* ngo = this->nodeGraphicsObject(_node_id);
  if (ngo == nullptr)
  {
    LOG_ERROR("slotOnNodeDoubleClicked : Failed to cast to NodeGraphicsObject");
    return;
  }

  // 获取该节点在场景中的中心位置
  QPointF scene_pos = ngo->sceneBoundingRect().center();
  if (this->views().empty())
  {
    LOG_ERROR("slotOnNodeDoubleClicked : Failed to FlowScene->views().empty()");
    return;
  }

  QPoint view_pos   = this->views().first()->mapFromScene(scene_pos);
  QPoint global_pos = this->views().first()->viewport()->mapToGlobal(view_pos);

  node_model->showProperty(global_pos);
}

void FlowScene::slotOnNodeCreate(const QtNodes::NodeId _node_id)
{
  LOG_DEBUG("创建节点, node id : {}", _node_id);
  auto& model_ptr        = graphModel();
  auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
  if (data_graph_model == nullptr)
  {
    LOG_ERROR("Failed to cast to DataFlowGraphModel");
    return;
  }
  BtNodeModel* node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
  if (node_model == nullptr)
  {
    LOG_ERROR("Failed to cast to BtNodeModel");
    return;
  }

  // 为每个节点实例生成唯一的内部名称（用于BehaviorTree执行）
  QString unique_instance_name = QString("%1_%2").arg(node_model->registrationName()).arg(_node_id);
  node_model->setInstanceName(unique_instance_name);

  // 设置用户友好的显示名称（在属性面板中显示）
  if (node_model->model() && node_model->model()->property_model)
  {
    // 设置一个用户友好的默认显示名称
    QString user_friendly_name = node_model->registrationName();
    node_model->model()->property_model->setValue("唯一名称", "display_name", user_friendly_name.toStdString());
  }

  // 使用实例名称作为映射键，确保每个节点实例都有唯一的映射
  register_node_id_map_[node_model->instanceName()] = _node_id;
  LOG_DEBUG("slotOnNodeCreate, node id : {}, registration_name : {}, instance_name : {}", _node_id,
            node_model->registrationName(), node_model->instanceName());
}

void FlowScene::slotNodeContextMenu(QtNodes::NodeId const _node_id, QPointF const _scene_pos)
{
  Q_UNUSED(_scene_pos);

  LOG_DEBUG("Node context menu requested for Node ID: {}", _node_id);

  if (_node_id == QtNodes::InvalidNodeId)
  {
    LOG_ERROR("Node context menu InvalidNodeId requested for Node ID: {}", _node_id);
    return;
  }

  // 检查编辑锁定状态
  if (is_edit_locked_)
  {
    LOG_DEBUG("slotNodeContextMenu: 编辑已锁定，无法显示右键菜单");
    return;
  }

  // 创建菜单
  QMenu context_menu;
  context_menu.addSeparator();
  QAction* delete_action = context_menu.addAction("删除节点");
  context_menu.addSeparator();
  QAction* reset_state_action = context_menu.addAction("重置状态");
  context_menu.addSeparator();
  QAction* properties_action = context_menu.addAction("属性...");

  // 删除
  connect(delete_action, &QAction::triggered, this, [this, _node_id]() {
    auto& model_ptr        = graphModel();  // 直接获取 DataFlowGraphModel 引用
    auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
    if (data_graph_model == nullptr)
    {
      LOG_ERROR("Failed to cast to DataFlowGraphModel");
      return;
    }
    BtNodeModel* node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
    if (node_model == nullptr)
    {
      LOG_ERROR("Failed to cast to BtNodeModel");
      return;
    }
    register_node_id_map_.erase(node_model->registrationName());
    LOG_DEBUG("Node removed, node id : {}, name : {}", _node_id, node_model->registrationName());
    model_ptr.deleteNode(_node_id);
  });

  // 重置状态
  connect(reset_state_action, &QAction::triggered, this, [this, _node_id]() {
    auto& model_ptr        = graphModel();
    auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
    if (data_graph_model == nullptr)
    {
      LOG_ERROR("Failed to cast to DataFlowGraphModel");
      return;
    }
    auto* node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
    node_model->updateNodeInfo(BT::Duration(), "", BT::NodeStatus::IDLE, BT::NodeStatus::IDLE);
  });

  // 属性
  connect(properties_action, &QAction::triggered, this,
          [_node_id]() { LOG_DEBUG("Properties requested for Node ID: {}", _node_id); });

  // 使用全局鼠标光标位置
  context_menu.exec(QCursor::pos());
}

void FlowScene::slotNodeStatusChanged(BT::Duration _timestamp,
                                      const std::string& _name,
                                      BT::NodeStatus _prev_status,
                                      BT::NodeStatus _status,
                                      const QVariantMap& _output_ports)
{
  // 检查 uid 是否在我们的映射中
  auto id_iter = register_node_id_map_.find(QString::fromStdString(_name));
  if (id_iter != register_node_id_map_.end())
  {
    auto node_id = id_iter->second;

    auto& model_ptr = graphModel();
    if (model_ptr.nodeExists(node_id))
    {
      auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
      if (data_graph_model == nullptr)
      {
        LOG_ERROR("Failed to cast to DataFlowGraphModel");
        return;
      }
      auto* node_model = data_graph_model->delegateModel<BtNodeModel>(node_id);
      if (node_model == nullptr)
      {
        LOG_ERROR("Failed to cast to BtNodeModel");
        return;
      }
      if (BT::NodeStatus::IDLE != _status)
      {
        node_model->updateNodeInfo(_timestamp, _name, _prev_status, _status, _output_ports);
      }
    }
    else
    {
      LOG_DEBUG("Node ID {} no longer exists in the graph model.", node_id);
      // 可以考虑从 map 中移除无效的 ID
      register_node_id_map_.erase(id_iter);
    }
  }
  else
  {
    // 如果 uid 未找到，可能是不需要可视化的节点或映射错误
    LOG_DEBUG("BT uid {} not fount in the mapping", _name);
  }
}

FlowView::FlowView(QWidget* _parent) : QtNodes::GraphicsView(_parent) { setAcceptDrops(true); }

FlowView::~FlowView() = default;

bool FlowView::canAcceptMimeData(const QMimeData* _mime_data)
{
  // Check if the MIME data format is the one we defined in NodeTreeWidget
  return _mime_data->hasFormat(G_MIME_TYPE);
}

void FlowView::dragEnterEvent(QDragEnterEvent* _event)
{
  // 检查编辑锁定状态
  auto* flow_scene = dynamic_cast<FlowScene*>(scene());
  if (flow_scene != nullptr && flow_scene->isEditLocked())
  {
    _event->ignore();
    return;
  }

  if (canAcceptMimeData(_event->mimeData()))
  {
    // Accept the drag if the format is correct
    _event->acceptProposedAction();
  }
  else
  {
    // Ignore if the format is not correct
    _event->ignore();
  }
}

void FlowView::dragMoveEvent(QDragMoveEvent* _event)
{
  // 检查编辑锁定状态
  auto* flow_scene = dynamic_cast<FlowScene*>(scene());
  if (flow_scene != nullptr && flow_scene->isEditLocked())
  {
    _event->ignore();
    return;
  }

  if (canAcceptMimeData(_event->mimeData()))
  {
    // Accept the move if the format is correct
    _event->acceptProposedAction();
  }
  else
  {
    // Ignore if the format is not correct
    _event->ignore();
  }
}

void FlowView::dropEvent(QDropEvent* _event)
{
  if (!canAcceptMimeData(_event->mimeData()))
  {
    _event->ignore();
    return;
  }

  auto* scene = dynamic_cast<FlowScene*>(this->scene());
  if (scene == nullptr)
  {
    LOG_ERROR("FlowView::dropEvent: scene is nullptr");
    return;
  }

  // 检查编辑锁定状态
  if (scene->isEditLocked())
  {
    _event->ignore();
    return;
  }

  _event->acceptProposedAction();

  auto encoded_data = _event->mimeData()->data(G_MIME_TYPE);
  QDataStream stream(&encoded_data, QIODevice::ReadOnly);
  QString registered_model_name;
  QString category;
  stream >> registered_model_name >> category;

  if (registered_model_name.isEmpty())
  {
    LOG_DEBUG("FlowView::dropEvent: Drop event: Empty registered model name");
    _event->ignore();
    return;
  }

  QPointF pos = mapToScene(mapFromGlobal(QCursor::pos()));
  auto& model = scene->graphModel();

  QtNodes::NodeId const NEW_ID = model.addNode(registered_model_name);
  model.setNodeData(NEW_ID, QtNodes::NodeRole::Position, pos);
}

void FlowView::mousePressEvent(QMouseEvent* _event)
{
  auto* scene = dynamic_cast<FlowScene*>(this->scene());
  if (scene != nullptr && scene->isEditLocked())
  {
    // 锁定状态下，检查是否点击在节点上
    QPointF scene_pos   = mapToScene(_event->pos());
    QGraphicsItem* item = scene->itemAt(scene_pos, transform());

    if (item != nullptr)
    {
      // 如果点击在节点或连接线上，阻止操作
      return;
    }

    // 如果点击在空白区域，允许视野拖动
  }

  QtNodes::GraphicsView::mousePressEvent(_event);
}

void FlowView::mouseMoveEvent(QMouseEvent* _event)
{
  auto* scene = dynamic_cast<FlowScene*>(this->scene());
  if (scene != nullptr && scene->isEditLocked())
  {
    // 锁定状态下，检查是否在拖拽模式
    if (dragMode() == QGraphicsView::ScrollHandDrag || ((_event->buttons() & Qt::MiddleButton) != 0) ||
        ((_event->buttons() & Qt::LeftButton) != 0 && (_event->modifiers() & Qt::AltModifier) != 0))
    {
      // 允许视野拖动（滚动手拖拽模式、中键拖拽、Alt+左键拖拽）
      QtNodes::GraphicsView::mouseMoveEvent(_event);
      return;
    }

    // 阻止其他拖拽操作（节点拖拽等）
    return;
  }

  QtNodes::GraphicsView::mouseMoveEvent(_event);
}

void FlowView::mouseReleaseEvent(QMouseEvent* _event)
{
  auto* scene = dynamic_cast<FlowScene*>(this->scene());
  if (scene == nullptr)
  {
    LOG_ERROR("FlowView::mouseReleaseEvent: scene is nullptr");
    return;
  }

  // 检查编辑锁定状态
  if (scene->isEditLocked())
  {
    // 锁定状态下，检查是否是视野拖动操作
    if (dragMode() == QGraphicsView::ScrollHandDrag || ((_event->button() & Qt::MiddleButton) != 0) ||
        ((_event->button() & Qt::LeftButton) != 0 && (_event->modifiers() & Qt::AltModifier) != 0))
    {
      // 允许完成视野拖动
      QtNodes::GraphicsView::mouseReleaseEvent(_event);
      return;
    }

    // 阻止节点移动操作
    return;
  }

  QtNodes::GraphicsView::mouseReleaseEvent(_event);

  auto& model = scene->graphModel();

  // 遍历所有节点，更新它们的位置
  for (QtNodes::NodeId node_id : model.allNodeIds())
  {
    auto new_pos = scene->nodeGraphicsObject(node_id)->pos();
    model.setNodeData(node_id, QtNodes::NodeRole::Position, new_pos);
  }
}

TestFlowEditor::TestFlowEditor(QWidget* _parent) : QWidget(_parent)
{
  setFlowView();

  // 设置窗口标题
  setWindowTitle("Test Flow Editor");
}

TestFlowEditor::~TestFlowEditor() = default;

std::shared_ptr<QtNodes::NodeDelegateModelRegistry> TestFlowEditor::registerDataModels()
{
  auto registry = NodeManager::getInstance()->qtNodeRegistry();

  auto node_creator = [&](const QString& _id, std::shared_ptr<NodeModel> _model) {
    QString category = QString::fromStdString(BT::toStr(_model->type));
    if (_id == "开始")
    {
      category = "根节点";
    }
    auto factory = [_model]() {
      NodeModel* model_ptr = _model.get();
      return std::make_unique<BtNodeModel>(model_ptr);
    };
    registry->registerModel<BtNodeModel>(std::move(factory), category);
  };

  for (const auto& model : builtinNodeModels())
  {
    auto model_ptr = std::make_shared<NodeModel>();
    // 复制必要的属性
    model_ptr->type            = model.second->type;
    model_ptr->registration_id = model.second->registration_id;
    model_ptr->instance_name   = model.second->instance_name;
    model_ptr->display_name    = model.second->display_name;
    model_ptr->description     = model.second->description;
    // 复制端口信息
    model_ptr->ports = model.second->ports;
    // 共享PropertyModel
    model_ptr->property_model = model.second->property_model;

    tree_node_models_[model.first] = model_ptr;
  }

  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::CONTROL;
    model_ptr->registration_id    = "Sequence";
    model_ptr->instance_name      = "Sequence";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Sequence"] = model_ptr;
  }
  {
    auto model_ptr                     = std::make_shared<NodeModel>();
    model_ptr->type                    = BT::NodeType::CONTROL;
    model_ptr->registration_id         = "SequenceCheck";
    model_ptr->instance_name           = "SequenceCheck";
    model_ptr->ports                   = PortModels {};
    tree_node_models_["SequenceCheck"] = model_ptr;
  }
  {
    auto model_ptr                     = std::make_shared<NodeModel>();
    model_ptr->type                    = BT::NodeType::CONTROL;
    model_ptr->registration_id         = "AsyncSequence";
    model_ptr->instance_name           = "AsyncSequence";
    model_ptr->ports                   = PortModels {};
    tree_node_models_["AsyncSequence"] = model_ptr;
  }
  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::CONTROL;
    model_ptr->registration_id    = "Fallback";
    model_ptr->instance_name      = "Fallback";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Fallback"] = model_ptr;
  }
  {
    auto model_ptr                     = std::make_shared<NodeModel>();
    model_ptr->type                    = BT::NodeType::CONTROL;
    model_ptr->registration_id         = "AsyncFallback";
    model_ptr->instance_name           = "AsyncFallback";
    model_ptr->ports                   = PortModels {};
    tree_node_models_["AsyncFallback"] = model_ptr;
  }

  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::CONTROL;
    model_ptr->registration_id    = "Parallel";
    model_ptr->instance_name      = "Parallel";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Parallel"] = model_ptr;
  }
  {
    auto model_ptr                   = std::make_shared<NodeModel>();
    model_ptr->type                  = BT::NodeType::CONTROL;
    model_ptr->registration_id       = "ParallelAll";
    model_ptr->instance_name         = "ParallelAll";
    model_ptr->ports                 = PortModels {};
    tree_node_models_["ParallelAll"] = model_ptr;
  }
  {
    auto model_ptr                        = std::make_shared<NodeModel>();
    model_ptr->type                       = BT::NodeType::CONTROL;
    model_ptr->registration_id            = "ReactiveSequence";
    model_ptr->instance_name              = "ReactiveSequence";
    model_ptr->ports                      = PortModels {};
    tree_node_models_["ReactiveSequence"] = model_ptr;
  }

  {
    auto model_ptr                  = std::make_shared<NodeModel>();
    model_ptr->type                 = BT::NodeType::CONTROL;
    model_ptr->registration_id      = "IfThenElse";
    model_ptr->instance_name        = "IfThenElse";
    model_ptr->ports                = PortModels {};
    tree_node_models_["IfThenElse"] = model_ptr;
  }
  {
    auto model_ptr                   = std::make_shared<NodeModel>();
    model_ptr->type                  = BT::NodeType::CONTROL;
    model_ptr->registration_id       = "WhileDoElse";
    model_ptr->instance_name         = "WhileDoElse";
    model_ptr->ports                 = PortModels {};
    tree_node_models_["WhileDoElse"] = model_ptr;
  }

  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "Sleep";
    model_ptr->instance_name   = "Sleep";
    PortModels ports;
    ports["延时"]              = { "msec", BT::PortDirection::INPUT, "延时", "1000" };
    model_ptr->ports           = std::move(ports);
    tree_node_models_["Sleep"] = model_ptr;
  }
  {
    PortModels ports;
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::CONDITION;
    model_ptr->registration_id = "ScriptCondition";
    model_ptr->instance_name   = "ScriptCondition";

    PortModel script_port;
    script_port.type_name     = "code";
    script_port.direction     = BT::PortDirection::INPUT;
    script_port.description   = "赋值运算符、字符串和数字:\n"
                                "param_A := 42\n"
                                "param_B = 3.14\n"
                                "message = 'hello world'\n"
                                "Tip:运算符`:=`和`=`"
                                "之间的区别在于，如果黑板上不存在新条目，前者可能会在黑板上创建一个新条目，而如果黑板上"
                                "没有该条目，后者将抛出异常。\n"
                                "\n"
                                "支持算术运算符和括号:\n"
                                "param_A := 7\n"
                                "param_B := 5\n"
                                "param_B *= 2\n"
                                "param_C := (param_A * 3) + param_B\n"
                                "\n"
                                "位运算符和十六进制数:\n"
                                "value:= 0x7F\n"
                                "val_A:= value & 0x0F\n"
                                "val_B:= value | 0xF0\n"
                                "\n"
                                "逻辑和比较运算符:\n"
                                "val_A := true\n"
                                "val_B := 5 > 3\n"
                                "val_C := (val_A == val_B)\n"
                                "val_D := (val_A && val_B) || !val_C\n"
                                "val_B = (val_A > 1) ? 42 : 24\n";
    script_port.default_value = "";
    script_port.data_type     = PortDataType::STRING;
    script_port.height_factor = 2;  // 设置地址字段的高度为标准高度的3倍，方便输入更多地址，自动启用多行模式
    ports.insert({ "条件脚本", std::move(script_port) });
    model_ptr->ports                     = std::move(ports);
    tree_node_models_["ScriptCondition"] = model_ptr;
  }
  {
    auto model_ptr                    = std::make_shared<NodeModel>();
    model_ptr->type                   = BT::NodeType::DECORATOR;
    model_ptr->registration_id        = "ForceSuccess";
    model_ptr->instance_name          = "ForceSuccess";
    model_ptr->ports                  = PortModels {};
    tree_node_models_["ForceSuccess"] = model_ptr;
  }

  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::DECORATOR;
    model_ptr->registration_id    = "Inverter";
    model_ptr->instance_name      = "Inverter";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Inverter"] = model_ptr;
  }
  {
    auto model_ptr                            = std::make_shared<NodeModel>();
    model_ptr->type                           = BT::NodeType::DECORATOR;
    model_ptr->registration_id                = "RetryUntilSuccessful";
    model_ptr->instance_name                  = "RetryUntilSuccessful";
    model_ptr->ports                          = PortModels {};
    tree_node_models_["RetryUntilSuccessful"] = model_ptr;
  }
  {
    auto model_ptr                               = std::make_shared<NodeModel>();
    model_ptr->type                              = BT::NodeType::DECORATOR;
    model_ptr->registration_id                   = "KeepRunningUntilFailure";
    model_ptr->instance_name                     = "KeepRunningUntilFailure";
    model_ptr->ports                             = PortModels {};
    tree_node_models_["KeepRunningUntilFailure"] = model_ptr;
  }
  {
    auto model_ptr              = std::make_shared<NodeModel>();
    model_ptr->type             = BT::NodeType::DECORATOR;
    model_ptr->registration_id  = "Repeat";
    model_ptr->instance_name    = "Repeat";
    model_ptr->ports            = PortModels {};
    tree_node_models_["Repeat"] = model_ptr;
  }
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "Timeout";
    model_ptr->instance_name   = "Timeout";
    PortModels ports;
    ports["延时"]                = { "msec", BT::PortDirection::INPUT, "延时", "1000" };
    model_ptr->ports             = std::move(ports);
    tree_node_models_["Timeout"] = model_ptr;
  }
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "Delay";
    model_ptr->instance_name   = "Delay";
    PortModels ports;
    ports["延时"]              = { "msec", BT::PortDirection::INPUT, "延时", "1000" };
    model_ptr->ports           = std::move(ports);
    tree_node_models_["Delay"] = model_ptr;
  }
  {
    auto model_ptr               = std::make_shared<NodeModel>();
    model_ptr->type              = BT::NodeType::DECORATOR;
    model_ptr->registration_id   = "RunOnce";
    model_ptr->instance_name     = "RunOnce";
    model_ptr->ports             = PortModels {};
    tree_node_models_["RunOnce"] = model_ptr;
  }
  {
    auto model_ptr                    = std::make_shared<NodeModel>();
    model_ptr->type                   = BT::NodeType::DECORATOR;
    model_ptr->registration_id        = "ForceSuccess";
    model_ptr->instance_name          = "ForceSuccess";
    model_ptr->ports                  = PortModels {};
    tree_node_models_["ForceSuccess"] = model_ptr;
  }
  {
    auto model_ptr                    = std::make_shared<NodeModel>();
    model_ptr->type                   = BT::NodeType::DECORATOR;
    model_ptr->registration_id        = "ForceFailure";
    model_ptr->instance_name          = "ForceFailure";
    model_ptr->ports                  = PortModels {};
    tree_node_models_["ForceFailure"] = model_ptr;
  }

  for (auto& model : tree_node_models_)
  {
    QString id = QString::fromStdString(model.first);
    node_creator(id, model.second);
    LOG_INFO("加载 node model: {}", model.first);
  }

  return registry;
}

void TestFlowEditor::setFlowView()
{
  if (nullptr != ptr_)
  {
    ptr_.reset();
    this->setLayout(nullptr);
  }

  auto model = std::make_unique<QtNodes::DataFlowGraphModel>(TestFlowEditor::registerDataModels());
  auto scene = std::make_unique<FlowScene>(*model, this);
  DataSerializer::getInstance()->setDataFlow(scene.get(), model.get());
  auto flow_view             = std::make_unique<FlowView>(this);
  auto status_signal_emitter = std::make_unique<StatusSignalEmitter>();
  flow_view->viewport()->setCursor(Qt::OpenHandCursor);
  flow_view->setDragMode(QGraphicsView::ScrollHandDrag);
  flow_view->setScene(scene.get());
  connect(status_signal_emitter.get(), &StatusSignalEmitter::signalStatusChanged, scene.get(),
          &FlowScene::slotNodeStatusChanged, Qt::QueuedConnection);
  ptr_ =
    std::make_unique<EditorPtr>(model.release(), scene.release(), flow_view.release(), status_signal_emitter.release());

  auto* layout = new QVBoxLayout(this);
  layout->setContentsMargins(0, 0, 0, 0);
  layout->setSpacing(0);
  layout->addWidget(&ptr_->get<FlowView>());
  this->setLayout(layout);
}

bool TestFlowEditor::saveToFile(const std::string& _file_path)
{
  auto res = DataSerializer::getInstance()->saveToUnifiedFile(QString::fromStdString(_file_path));

  if (!res)
  {
    LOG_ERROR("验证编辑后的节点数据保存失败，不符合规则, 请检测!");
    return false;
  }

  current_file_path_ = _file_path;

  return true;
}

bool TestFlowEditor::loadFromFile(const std::string& _file_path)
{
  return DataSerializer::getInstance()->loadFromUnifiedFile(QString::fromStdString(_file_path));
}

bool TestFlowEditor::startTest()
{
  // clear ui status
  for (const auto& node_id : ptr_->get<NodeGraphModel>().allNodeIds())
  {
    auto* node_model = ptr_->get<NodeGraphModel>().delegateModel<BtNodeModel>(node_id);
    if (node_model == nullptr)
    {
      LOG_ERROR("Failed to cast to BtNodeModel");
      continue;
    }

    node_model->updateNodeInfo(BT::Duration(), "", BT::NodeStatus::IDLE, BT::NodeStatus::IDLE);
  }

  bool res = false;
  std::thread([&]() {
    try
    {
      LOG_INFO("开始测试 ...");

      auto tree =
        NodeManager::getInstance()->btFactory()->createTree("MainTree", NodeManager::getInstance()->globalBlackboard());

      QtNodesStatusLogger logger(tree, &ptr_->get<StatusSignalEmitter>());
      logger.setEnabled(true);

      // helper function to print the tree
      BT::printTreeRecursively(tree.rootNode());

      auto json = ExportTreeToJSON(tree);
      tree.tickWhileRunning();
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("XML 解析出错: {}", std::string(e.what()));
      res = false;
      return;
    }
    catch (...)
    {
      LOG_ERROR("XML 解析时发生未知错误！");
      res = false;
      return;
    }
    LOG_INFO("测试结束");
    res = true;
  }).detach();

  return res;
}

void TestFlowEditor::nodeReorder()
{
  {
    const QSignalBlocker BLOCKER(this);
    auto abstract_tree = Utils::buildTreeFromScene(&ptr_->get<NodeGraphModel>(), QtNodes::InvalidNodeId);
    abstract_tree.debugPrint();
    Utils::nodeReorder(&ptr_->get<NodeGraphModel>(), abstract_tree);
  }
}

void TestFlowEditor::setEditLocked(bool _locked)
{
  if (ptr_)
  {
    ptr_->get<FlowScene>().setEditLocked(_locked);
  }
}

}  // namespace robosense::lidar