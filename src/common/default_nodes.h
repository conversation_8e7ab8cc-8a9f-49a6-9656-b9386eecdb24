/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the BSD 3-Clause License.
 * You may obtain a copy of the License at:
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its contributors
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DEFAULT_NODES_H
#define DEFAULT_NODES_H

#include <behaviortree_cpp/action_node.h>
#include <behaviortree_cpp/bt_factory.h>
#include <string>

namespace robosense::lidar
{

/**
 * @brief 打印变量节点 - 根据输入变量打印其结果
 *
 * 这是一个同步动作节点，用于接收一个输入变量并将其值打印到控制台。
 * 支持字符串类型的输入，可以显示变量的名称和值。
 */
class PrintVariableNode : public BT::SyncActionNode
{
public:
  /**
   * @brief 构造函数
   * @param _name 节点名称
   * @param _config 节点配置
   */
  PrintVariableNode(const std::string& _name, const BT::NodeConfig& _config);

  /**
   * @brief 析构函数
   */
  ~PrintVariableNode() override = default;

  // 禁用拷贝和移动
  PrintVariableNode(const PrintVariableNode&) = delete;
  PrintVariableNode& operator=(const PrintVariableNode&) = delete;
  PrintVariableNode(PrintVariableNode&&)                 = delete;
  PrintVariableNode& operator=(PrintVariableNode&&) = delete;

  /**
   * @brief 执行节点逻辑
   * @return 节点执行状态
   */
  BT::NodeStatus tick() override;

  /**
   * @brief 提供端口列表
   * @return 端口列表，包含输入端口定义
   */
  static BT::PortsList providedPorts();

private:
  /**
   * @brief 将Any类型的值转换为字符串用于显示
   * @param _value Any类型的值
   * @return 转换后的字符串
   */
  static std::string convertAnyToString(const BT::Any& _value);

private:
  std::string node_name_;  ///< 节点名称缓存
};

/**
 * @brief 注册默认节点到BehaviorTree工厂
 * @param _factory BehaviorTree工厂引用
 */
void registerDefaultNodes(BT::BehaviorTreeFactory& _factory);

}  // namespace robosense::lidar

#endif  // DEFAULT_NODES_H
