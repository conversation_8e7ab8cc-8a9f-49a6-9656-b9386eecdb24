﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef ABS_BEHAVIOR_TREE_H
#define ABS_BEHAVIOR_TREE_H

#include "3rd_party/tsl/ordered_map.h"
#include "common_types.h"
#include "property/property_model.h"
#include <QPointF>
#include <QSizeF>
#include <QString>
#include <QtCore/QObject>
#include <QtNodes/Definitions>
#include <QtNodes/internal/Definitions.hpp>
#include <behaviortree_cpp/basic_types.h>
#include <behaviortree_cpp/bt_factory.h>
#include <behaviortree_cpp/tree_node.h>
#include <deque>
#include <map>
#include <rsfsc_log/fmt/magic_enum.hpp>

namespace robosense::lidar
{

enum class PortLayout : std::uint8_t
{
  Vertical,
  Horizontal
};

using PortsMapping = std::map<QString, QString>;

// 端口数据类型枚举
enum class PortDataType : std::uint8_t
{
  STRING,         // 字符串类型
  INTEGER,        // 整数类型
  DOUBLE,         // 浮点数类型
  BOOLEAN,        // 布尔类型
  ENUM,           // 枚举类型
  FILE_PATH,      // 文件路径类型
  DIRECTORY_PATH  // 文件夹路径类型
};

// 获取端口数据类型的字符串表示
inline QString portDataTypeToString(PortDataType _type)
{
  auto name = magic_enum::enum_name(_type);
  return name.empty() ? "Unknown" : QString::fromUtf8(name.data(), static_cast<int>(name.size()));
}

// 根据类型名称推断端口数据类型
inline PortDataType inferPortDataType(const QString& _type_name)
{
  QString type_lower = _type_name.toLower();

  if (type_lower.contains("int") || type_lower.contains("uint") || type_lower.contains("long") ||
      type_lower.contains("short") || type_lower.contains("size_t"))
  {
    return PortDataType::INTEGER;
  }

  if (type_lower.contains("double") || type_lower.contains("float"))
  {
    return PortDataType::DOUBLE;
  }

  if (type_lower.contains("bool"))
  {
    return PortDataType::BOOLEAN;
  }

  if (type_lower.contains("enum"))
  {
    return PortDataType::ENUM;
  }

  if (type_lower.contains("filepath") || type_lower.contains("file_path") || type_lower.contains("file path"))
  {
    return PortDataType::FILE_PATH;
  }

  if (type_lower.contains("directorypath") || type_lower.contains("directory_path") ||
      type_lower.contains("directory path") || type_lower.contains("folderpath") ||
      type_lower.contains("folder_path") || type_lower.contains("folder path"))
  {
    return PortDataType::DIRECTORY_PATH;
  }

  // 默认返回字符串类型
  return PortDataType::STRING;
}

// alternative type, similar to BT::PortInfo
struct PortModel
{
  PortModel() = default;

  QString type_name;                                // 类型名称
  BT::PortDirection direction {};                   // 端口方向
  QString description;                              // 描述
  QString default_value;                            // 默认值
  PortDataType data_type { PortDataType::STRING };  // 数据类型

  // 枚举类型的选项列表
  QStringList enum_options;

  // 数值类型的范围
  double min_value { std::numeric_limits<double>::lowest() };
  double max_value { std::numeric_limits<double>::max() };
  double step { 1.0 };
  int decimals { 2 };  // 浮点数小数位数

  // 字符串类型的高度倍数，大于1时自动启用多行模式
  int height_factor { 1 };  // 默认为1倍高度

  PortModel& operator=(const BT::PortInfo& _src);
};

using PortModels = tsl::ordered_map<std::string, PortModel>;

struct NodeModel
{
  NodeModel();
  ~NodeModel() = default;

  NodeModel(const NodeModel& _other) = delete;
  NodeModel& operator=(const NodeModel& _other) = default;
  NodeModel(NodeModel&& _other) noexcept        = delete;
  NodeModel& operator=(NodeModel&& _other) noexcept = default;
  bool operator==(const NodeModel& _other) const;
  bool operator!=(const NodeModel& _other) const { return !(*this == _other); }
  NodeModel& operator=(const BT::TreeNodeManifest& _src);

  // 构造函数 - 使用基本参数初始化
  NodeModel(BT::NodeType _type,
            QStringView _registration_id,
            QStringView _display_name,
            QStringView _description = QStringView());

  // 移动构造函数 - 使用std::move避免拷贝
  NodeModel(BT::NodeType _type,
            QString&& _registration_id,
            QString&& _display_name,
            QString&& _description = QString());

  // 初始化PropertyModel并绑定节点信息
  void initPropertyModel();

  // 设置实例名称 - 同时更新PropertyModel
  void setInstanceName(QStringView _name);

  // 设置实例名称 - 接受字符串字面量
  void setInstanceName(const char* _name);

  // 设置端口值 - 同时更新PropertyModel
  void setPortValue(QStringView _port_name, QStringView _value);

  // 设置端口值 - 接受字符串字面量
  void setPortValue(const char* _port_name, const char* _value);

  // 获取端口值 - 从ports_中查找并返回
  [[nodiscard]] QString getPortValue(QStringView _port_name) const;

  // 获取端口值 - 接受字符串字面量
  [[nodiscard]] QString getPortValue(const char* _port_name) const;

  // 获取节点类型信息 - 用于创建NodeInfo
  [[nodiscard]] NodeInfo getNodeInfo(std::string_view _category) const;

  // 获取节点类型信息 - 接受字符串字面量
  [[nodiscard]] NodeInfo getNodeInfo(const char* _category) const;

  BT::NodeType type {};                           // 节点类型
  QString registration_id;                        // 注册ID
  PortModels ports;                               // 端口模型
  QString instance_name;                          // 实例名称
  QString display_name;                           // 显示名称
  QString description;                            // 描述信息
  PropertyModel::Ptr property_model { nullptr };  // 属性模型
};

using NodeModels = tsl::ordered_map<std::string, std::shared_ptr<NodeModel>>;

enum class GraphicMode : std::uint8_t
{
  EDITOR,
  MONITOR,
  REPLAY
};

GraphicMode getGraphicModeFromString(const QString& str);

const char* toStr(GraphicMode _type);

const NodeModels& builtinNodeModels();

struct AbstractTreeNode
{
  AbstractTreeNode() : model(std::make_shared<NodeModel>()), graphic_node(QtNodes::InvalidNodeId)
  {
    model->type = BT::NodeType::UNDEFINED;
  }

  // 使用默认的复制和移动操作
  AbstractTreeNode(const AbstractTreeNode&) = default;
  AbstractTreeNode& operator=(const AbstractTreeNode&) = default;
  AbstractTreeNode(AbstractTreeNode&&)                 = default;
  AbstractTreeNode& operator=(AbstractTreeNode&&) = default;
  ~AbstractTreeNode()                             = default;

  std::shared_ptr<NodeModel> model;  // 使用shared_ptr存储NodeModel
  PortsMapping ports_mapping;
  int index {};
  QString instance_name;
  BT::NodeStatus status {};
  QSizeF size;
  QPointF pos;  // top left corner
  std::vector<int> children_index;
  QtNodes::NodeId graphic_node;

  bool operator==(const AbstractTreeNode& _other) const;

  bool operator!=(const AbstractTreeNode& _other) const { return !(*this == _other); }
};

class AbsBehaviorTree
{
public:
  using NodesVector = std::deque<AbstractTreeNode>;

  AbsBehaviorTree() = default;

  ~AbsBehaviorTree();

  AbsBehaviorTree(const AbsBehaviorTree&) = default;
  AbsBehaviorTree& operator=(const AbsBehaviorTree&) = default;
  AbsBehaviorTree(AbsBehaviorTree&&) noexcept        = default;
  AbsBehaviorTree& operator=(AbsBehaviorTree&&) noexcept = default;

  [[nodiscard]] size_t nodesCount() const { return nodes_.size(); }

  [[nodiscard]] const NodesVector& nodes() const { return nodes_; }

  NodesVector& nodes() { return nodes_; }

  [[nodiscard]] const AbstractTreeNode* node(size_t _index) const { return &nodes_.at(_index); }

  AbstractTreeNode* node(size_t _index) { return &nodes_.at(_index); }

  AbstractTreeNode* rootNode();

  [[nodiscard]] const AbstractTreeNode* rootNode() const;

  std::vector<const AbstractTreeNode*> findNodes(const QString& _instance_name);

  const AbstractTreeNode* findFirstNode(const QString& _instance_name);

  AbstractTreeNode* addNode(AbstractTreeNode* _parent, AbstractTreeNode&& _new_node);

  void debugPrint() const;

  bool operator==(const AbsBehaviorTree& _other) const;

  bool operator!=(const AbsBehaviorTree& _other) const { return !(*this == _other); }

  void clear();

  static int getUID();
  static const QString& getRootId();

private:
  NodesVector nodes_;
};

}  // namespace robosense::lidar

Q_DECLARE_METATYPE(robosense::lidar::AbsBehaviorTree);

Q_DECLARE_METATYPE(BT::Duration);
Q_DECLARE_METATYPE(std::string);
Q_DECLARE_METATYPE(BT::NodeStatus);

#endif  // ABS_BEHAVIOR_TREE_H
