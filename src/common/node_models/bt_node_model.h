﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef BT_NODE_MODEL_H
#define BT_NODE_MODEL_H

#include "abs_behavior_tree.h"
#include "behaviortree_cpp/basic_types.h"
#include "property/property_view.h"
#include <QEvent>
#include <QFile>
#include <QFormLayout>
#include <QLabel>
#include <QLineEdit>
#include <QSettings>
#include <QSvgRenderer>
#include <QWidget>
#include <QtCore/QObject>
#include <QtNodes/NodeDelegateModel>
#include <QtNodes/internal/NodeDelegateModel.hpp>
#include <memory>

namespace robosense::lidar
{

class PropertyView;

class BtNodeModel : public QtNodes::NodeDelegateModel
{
  Q_OBJECT

public:
  explicit BtNodeModel(NodeModel* _model);

  ~BtNodeModel() override;

  BtNodeModel(const BtNodeModel&) = delete;
  BtNodeModel& operator=(const BtNodeModel&) = delete;
  BtNodeModel(BtNodeModel&&) noexcept        = delete;
  BtNodeModel& operator=(BtNodeModel&&) noexcept = delete;

public:
  [[nodiscard]] BT::NodeType nodeType() const;

  virtual void setInstanceName(const QString& _name);

public:
  void initWidget();

  [[nodiscard]] unsigned int nPorts(QtNodes::PortType _port_type) const override;

  [[nodiscard]] QtNodes::ConnectionPolicy portOutConnectionPolicy(QtNodes::PortIndex) const;

  [[nodiscard]] QtNodes::NodeDataType dataType(QtNodes::PortType _port_type,
                                               QtNodes::PortIndex _port_index) const override;

  std::shared_ptr<QtNodes::NodeData> outData(QtNodes::PortIndex _port_index) override;

  void setInData(std::shared_ptr<QtNodes::NodeData> _node_data, QtNodes::PortIndex _port) override {}

  [[nodiscard]] const QString& registrationName() const;

  [[nodiscard]] std::shared_ptr<NodeModel> model() const { return model_ptr_; }

  [[nodiscard]] QString name() const override { return registrationName(); }
  [[nodiscard]] QString caption() const override { return registrationName(); }

  [[nodiscard]] const QString& instanceName() const;

  [[nodiscard]] PortsMapping getCurrentPortMapping() const;

  QWidget* embeddedWidget() override { return main_widget_; }

  [[nodiscard]] QJsonObject save() const override;

  void load(QJsonObject const& _json_object) override;

  void restore(QJsonObject const&);

  void updateNodeInfo(BT::Duration _timestamp,
                      const std::string& _name,
                      BT::NodeStatus _prev_status,
                      BT::NodeStatus _status,
                      const QVariantMap& _output_ports = {});

  void showProperty(const QPoint& _show_pos);

  // 保存属性窗口的几何信息（大小和位置）
  void savePropertyViewGeometry();

  // 恢复属性窗口的几何信息
  void restorePropertyViewGeometry();

private:
  // 从JSON对象中恢复UI属性参数
  void restoreProperties(const QJsonObject& _properties_json);

  void lock(bool _locked);

  // 设置端口映射，port_name为端口名称，port_value为端口值
  void setPortMapping(const QString& _port_name, const QString& _port_value);

  [[nodiscard]] int uid() const { return uid_; }

  bool eventFilter(QObject* _obj, QEvent* _event) override;

  [[nodiscard]] PropertyModel* propertyModel() const;

public Q_SLOTS:

  void updateNodeSize();
  void onHighlightPortValue(const QString& _value);

private:
  QWidget* main_widget_;

  QLineEdit* line_edit_name_;

  std::map<QString, QWidget*> ports_widgets_;
  int16_t uid_;

  QVBoxLayout* main_layout_;

  QLabel* name_label_;
  QLabel* state_label_;

  std::shared_ptr<NodeModel> model_ptr_;
  QSvgRenderer* icon_renderer_;

  PropertyView::Ptr property_view_ { nullptr };

  void readStyle();
  QString style_icon_;
  QColor style_caption_color_;
  QString style_caption_alias_;
  BT::NodeStatus runtime_status_ = BT::NodeStatus::IDLE;

  // 标记属性窗口几何信息是否已恢复
  bool property_view_geometry_restored_ = false;

Q_SIGNALS:

  void parameterUpdated(QString, QWidget*);
  void instanceNameChanged();
  void portValueDoubleClicked(QLineEdit* _value_port);
};

class LineEditNode : public QLineEdit
{
  Q_OBJECT
public:
  explicit LineEditNode(QWidget* _parent = nullptr) : QLineEdit(_parent) {}

  void mouseDoubleClickEvent(QMouseEvent* _event) override;
  void focusOutEvent(QFocusEvent* _event) override;
Q_SIGNALS:
  void doubleClicked();
  void lostFocus();
};

}  // namespace robosense::lidar

#endif  // BT_NODE_MODEL_H
