/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "bt_node_model.h"
#include "QtNodes/internal/NodeDelegateModel.hpp"
#include "node_utils.h"
#include "property/property_view.h"
#include <QApplication>
#include <QBoxLayout>
#include <QComboBox>
#include <QDebug>
#include <QFile>
#include <QFont>
#include <QFormLayout>
#include <QJsonDocument>
#include <QLineEdit>
#include <QPainter>
#include <QSizePolicy>
#include <QWidget>
#include <qboxlayout.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

const int G_MARGIN              = 10;
const int G_DEFAULT_LINE_WIDTH  = 100;
const int G_DEFAULT_FIELD_WIDTH = 50;
const int G_DEFAULT_LABEL_WIDTH = 50;

BtNodeModel::BtNodeModel(NodeModel* _model) :
  main_widget_(new QWidget()),
  line_edit_name_(nullptr),
  uid_(static_cast<int16_t>(AbsBehaviorTree::getUID())),
  main_layout_(new QVBoxLayout(main_widget_)),
  name_label_(new QLabel()),
  state_label_(new QLabel()),
  model_ptr_(std::make_shared<NodeModel>()),
  icon_renderer_(nullptr),
  style_caption_color_(QtNodes::NodeStyle().FontColor),
  style_caption_alias_((_model != nullptr) ? _model->registration_id : QString())
{
  // readStyle();
  // 初始化布局
  main_layout_->setMargin(0);
  main_layout_->setSpacing(2);
  main_widget_->setLayout(main_layout_);

  state_label_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
  state_label_->setFixedSize(20, 20);
  name_label_->setSizePolicy(QSizePolicy::Minimum, QSizePolicy::Fixed);
  name_label_->setMinimumSize(60, 20);

  main_widget_->setAttribute(Qt::WA_NoSystemBackground);
  main_widget_->setStyleSheet("color: white;");

  auto* grid_layout = new QGridLayout;

  grid_layout->addWidget(name_label_, 0, 0);
  grid_layout->addWidget(state_label_, 0, 1);

  main_layout_->addLayout(grid_layout);

  main_layout_->setSizeConstraint(QLayout::SizeConstraint::SetMaximumSize);

  updateNodeInfo(BT::Duration(), "", BT::NodeStatus::IDLE, BT::NodeStatus::IDLE);

  // 从_model复制必要的属性到model_ptr_
  if (_model != nullptr)
  {
    model_ptr_->type            = _model->type;
    model_ptr_->registration_id = _model->registration_id;
    model_ptr_->instance_name   = _model->instance_name;
    model_ptr_->display_name    = _model->display_name;
    model_ptr_->description     = _model->description;
    model_ptr_->ports           = _model->ports;
  }

  // 初始化NodeModel的PropertyModel
  model_ptr_->initPropertyModel();

  property_view_ = std::make_unique<PropertyView>(nullptr, true, model_ptr_->registration_id);
  property_view_->setModel(model_ptr_->property_model);
  property_view_->setButtonHidden(PropertyView::BtnHidden { true, true, true, true, true });

  name_label_->setText(model_ptr_->property_model->getValue<QString>("唯一名称", "display_name"));
  QObject::connect(model_ptr_->property_model.get(), &PropertyModel::signalValueChanged, this,
                   [this](const std::string& _group, const std::string& _key, const QVariant& _value) {
                     if (_group == "唯一名称" && _key == "display_name")
                     {
                       name_label_->setText(_value.toString());
                     }
                   });
}

BtNodeModel::~BtNodeModel() = default;

BT::NodeType BtNodeModel::nodeType() const
{
  if (!model_ptr_)
  {
    LOG_ERROR("nodeType : model_ptr_ is nullptr");
    return BT::NodeType::UNDEFINED;
  }
  return model_ptr_->type;
}

void BtNodeModel::initWidget()
{
  if (!style_icon_.isEmpty())
  {
    QFile file(style_icon_);
    if (!file.open(QIODevice::ReadOnly))
    {
      LOG_ERROR("file not opened: {}", style_icon_);
      file.close();
    }
    else
    {
      QByteArray bytes          = file.readAll();
      QByteArray new_color_fill = QString("fill:%1;").arg(style_caption_color_.name()).toUtf8();
      bytes.replace("fill:#ffffff;", new_color_fill);
      icon_renderer_ = new QSvgRenderer(bytes, this);
    }
  }

  updateNodeSize();
}

unsigned int BtNodeModel::nPorts(QtNodes::PortType _port_type) const
{
  if (!model_ptr_)
  {
    LOG_ERROR("nPorts : model_ptr_ is nullptr");
    return 0;
  }

  if (_port_type == QtNodes::PortType::Out)
  {
    if (nodeType() == BT::NodeType::ACTION || nodeType() == BT::NodeType::CONDITION)
    {
      return 0;
    }

    return 1;
  }
  if (_port_type == QtNodes::PortType::In)
  {
    return (model_ptr_->registration_id == AbsBehaviorTree::getRootId()) ? 0 : 1;
  }
  return 0;
}

QtNodes::ConnectionPolicy BtNodeModel::portOutConnectionPolicy(QtNodes::PortIndex /*_port_index*/) const
{
  if (!model_ptr_)
  {
    LOG_ERROR("portOutConnectionPolicy : model_ptr_ is nullptr");
    return QtNodes::ConnectionPolicy::One;
  }

  return (nodeType() == BT::NodeType::DECORATOR || model_ptr_->registration_id == AbsBehaviorTree::getRootId())
           ? QtNodes::ConnectionPolicy::One
           : QtNodes::ConnectionPolicy::Many;
}

void BtNodeModel::updateNodeSize()
{
  main_widget_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
  main_widget_->adjustSize();

  emit embeddedWidgetSizeUpdated();
}

QtNodes::NodeDataType BtNodeModel::dataType(QtNodes::PortType /*_port_type*/, QtNodes::PortIndex /*_port_index*/) const
{
  return QtNodes::NodeDataType { "", "" };
}

std::shared_ptr<QtNodes::NodeData> BtNodeModel::outData(QtNodes::PortIndex /*_port_index*/) { return nullptr; }

void BtNodeModel::readStyle()
{
  QFile style_file(":/NodesStyle.json");

  if (!style_file.open(QIODevice::ReadOnly))
  {
    qWarning("Couldn't open NodesStyle.json");
    return;
  }

  QByteArray bytearray = style_file.readAll();
  style_file.close();
  QJsonParseError error {};
  QJsonDocument json_doc(QJsonDocument::fromJson(bytearray, &error));

  if (json_doc.isNull())
  {
    LOG_ERROR("Failed to create JSON doc: {}", error.errorString());
    return;
  }
  if (!json_doc.isObject())
  {
    LOG_ERROR("JSON is not an object.");
    return;
  }

  QJsonObject toplevel_object = json_doc.object();

  if (toplevel_object.isEmpty())
  {
    LOG_ERROR("JSON Object is empty.");
    return;
  }

  if (!model_ptr_)
  {
    LOG_ERROR("model_ptr_ is nullptr");
    return;
  }

  QString model_type_name(QString::fromStdString(toStr(model_ptr_->type)));

  for (const auto& model_name : { model_type_name, model_ptr_->registration_id })
  {
    if (toplevel_object.contains(model_name))
    {
      auto category_style = toplevel_object[model_name].toObject();
      if (category_style.contains("icon"))
      {
        style_icon_ = category_style["icon"].toString();
      }
      if (category_style.contains("caption_color"))
      {
        style_caption_color_ = category_style["caption_color"].toString();
      }
      if (category_style.contains("caption_alias"))
      {
        style_caption_alias_ = category_style["caption_alias"].toString();
      }
    }
  }
}

const QString& BtNodeModel::registrationName() const
{
  static const QString empty_string;
  if (!model_ptr_)
  {
    LOG_ERROR("registrationName : model_ptr_ is nullptr");
    return empty_string;
  }
  return model_ptr_->registration_id;
}

const QString& BtNodeModel::instanceName() const
{
  static const QString EMPTY_STRING;
  if (!model_ptr_)
  {
    LOG_ERROR("instanceName : model_ptr_ is nullptr");
    return EMPTY_STRING;
  }
  return model_ptr_->instance_name;
}

PortsMapping BtNodeModel::getCurrentPortMapping() const
{
  PortsMapping out;

  for (const auto& iter : ports_widgets_)
  {
    const auto& label = iter.first;
    const auto& value = iter.second;

    if (auto* line_edit = dynamic_cast<QLineEdit*>(value))
    {
      out.insert(std::make_pair(label, line_edit->text()));
    }
    else if (auto* combo = dynamic_cast<QComboBox*>(value))
    {
      out.insert(std::make_pair(label, combo->currentText()));
    }
  }
  return out;
}

QJsonObject BtNodeModel::save() const
{
  QJsonObject model_json = QtNodes::NodeDelegateModel::save();  // NOTE 这里必须调用QtNodes::NodeDelegateModel::save()
  model_json["name"]     = registrationName();
  model_json["alias"] = instanceName();

  // 保存端口部件的值
  for (const auto& iter : ports_widgets_)
  {
    if (auto* line_edit = dynamic_cast<QLineEdit*>(iter.second))
    {
      model_json[iter.first] = line_edit->text();
    }
    else if (auto* combo = dynamic_cast<QComboBox*>(iter.second))
    {
      model_json[iter.first] = combo->currentText();
    }
  }

  // 保存UI属性参数
  if (model_ptr_ && model_ptr_->property_model)
  {
    QJsonObject properties_json;

    // 遍历所有组和属性
    for (const auto& [group_name, group] : model_ptr_->property_model->groups())
    {
      QJsonObject group_object;
      auto group_name_str = QString::fromStdString(group_name);

      // 遍历该组中的所有属性
      for (const auto& [full_key, property] : model_ptr_->property_model->properties())
      {
        QString full_key_str = QString::fromStdString(full_key);
        QStringList parts    = full_key_str.split('.');

        if (parts.size() == 2 && parts[0] == group_name_str)
        {
          const QString& key = parts[1];
          QVariant value     = property->value();
          group_object[key]  = QJsonValue::fromVariant(value);
        }
      }

      if (!group_object.isEmpty())
      {
        properties_json[group_name_str] = group_object;
      }
    }

    if (!properties_json.isEmpty())
    {
      model_json["properties"] = properties_json;
    }
  }

  return model_json;
}

void BtNodeModel::load(QJsonObject const& _json_object)
{
  try
  {
    QtNodes::NodeDelegateModel::load(_json_object);

    // 检查是否包含internal-data字段
    if (_json_object.contains("internal-data") && _json_object["internal-data"].isObject())
    {
      QJsonObject internal_data = _json_object["internal-data"].toObject();
      // 调用restore方法来恢复节点状态
      LOG_DEBUG("正在恢复节点状态: {}", internal_data["name"].toString());
      restore(internal_data);
    }
    else
    {
      // 直接使用传入的JSON对象
      LOG_DEBUG("直接使用JSON对象恢复节点状态");
      restore(_json_object);
    }
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("加载节点模型失败: {}", e.what());
  }
}

void BtNodeModel::restore(const QJsonObject& _model_json)
{
  LOG_INFO("开始恢复节点状态");

  // 检查name字段是否存在
  if (!_model_json.contains("name"))
  {
    LOG_WARN("JSON对象中没有name字段，尝试使用model-name字段");
    if (_model_json.contains("model-name"))
    {
      if (registrationName() != _model_json["model-name"].toString())
      {
        LOG_ERROR("节点类型不匹配: 期望 {} 但得到 {}", registrationName(), _model_json["model-name"].toString());
        return;
      }
    }
    else
    {
      LOG_ERROR("JSON对象中既没有name字段也没有model-name字段，无法恢复节点状态");
      return;
    }
  }
  else if (registrationName() != _model_json["name"].toString())
  {
    LOG_ERROR("节点类型不匹配: 期望 {} 但得到 {}", registrationName(), _model_json["name"].toString());
    return;
  }

  // 设置实例名称
  QString alias;
  if (_model_json.contains("alias"))
  {
    alias = _model_json["alias"].toString();
  }
  else if (_model_json.contains("name"))
  {
    // 如果没有alias字段，使用name字段作为实例名称
    alias = _model_json["name"].toString();
  }

  if (!alias.isEmpty())
  {
    LOG_INFO("设置节点实例名称: {}", alias);
    setInstanceName(alias);
  }

  // 恢复端口映射
  for (auto iter = _model_json.begin(); iter != _model_json.end(); iter++)
  {
    if (iter.key() != "alias" && iter.key() != "name" && iter.key() != "model-name" && iter.key() != "properties" &&
        iter.key() != "internal-data")
    {
      LOG_INFO("恢复端口映射: {} = {}", iter.key(), iter.value().toString());
      setPortMapping(iter.key(), iter.value().toString());
    }
  }

  // 恢复UI属性参数
  if (_model_json.contains("properties") && _model_json["properties"].isObject())
  {
    LOG_INFO("恢复UI属性参数");
    restoreProperties(_model_json["properties"].toObject());
  }

  LOG_INFO("节点状态恢复完成");
}

void BtNodeModel::restoreProperties(const QJsonObject& _properties_json)
{
  if (!model_ptr_)
  {
    LOG_ERROR("无法恢复属性：model_ptr_为空");
    return;
  }

  if (!model_ptr_->property_model)
  {
    LOG_ERROR("无法恢复属性：property_model为空");
    return;
  }

  // 遍历所有属性组
  for (auto group_iter = _properties_json.begin(); group_iter != _properties_json.end(); ++group_iter)
  {
    const QString& group_name = group_iter.key();

    if (!group_iter.value().isObject())
    {
      LOG_ERROR("属性组 {} 不是有效的JSON对象", group_name);
      continue;
    }

    QJsonObject group_object = group_iter.value().toObject();

    // 遍历该组中的所有属性
    for (auto prop_iter = group_object.begin(); prop_iter != group_object.end(); ++prop_iter)
    {
      const QString& key = prop_iter.key();
      QVariant value;

      // 根据JSON值类型转换为QVariant
      if (prop_iter.value().isDouble())
      {
        value = prop_iter.value().toDouble();
      }
      else if (prop_iter.value().isString())
      {
        value = prop_iter.value().toString();
      }
      else if (prop_iter.value().isBool())
      {
        value = prop_iter.value().toBool();
      }
      else if (prop_iter.value().isArray())
      {
        // 处理数组类型
        QJsonArray array = prop_iter.value().toArray();
        QVariantList list;
        for (const auto& item : array)
        {
          list.append(item.toVariant());
        }
        value = list;
      }
      else
      {
        value = prop_iter.value().toVariant();
      }

      // 更新属性模型中的值
      model_ptr_->property_model->setValue(group_name.toStdString(), key.toStdString(), value);
    }
  }
}

void BtNodeModel::lock(bool _locked)
{
  for (const auto& iter : ports_widgets_)
  {
    const auto& field_widget = iter.second;

    if (auto* lineedit = dynamic_cast<QLineEdit*>(field_widget))
    {
      lineedit->setReadOnly(_locked);
    }
    else if (auto* combo = dynamic_cast<QComboBox*>(field_widget))
    {
      combo->setEnabled(!_locked);
    }
  }
}

void BtNodeModel::setPortMapping(const QString& _port_name, const QString& _port_value)
{
  auto widget_iter = ports_widgets_.find(_port_name);
  if (widget_iter == ports_widgets_.end())
  {
    return;
  }

  auto* line_edit = dynamic_cast<QLineEdit*>(widget_iter->second);
  if (line_edit != nullptr)
  {
    line_edit->setText(_port_value);
    return;
  }

  auto* combo = dynamic_cast<QComboBox*>(widget_iter->second);
  if (combo == nullptr)
  {
    return;
  }

  int index = combo->findText(_port_value);
  if (index == -1)
  {
    LOG_ERROR("error, combo value {} not found", _port_value);
    return;
  }

  combo->setCurrentIndex(index);
}

bool BtNodeModel::eventFilter(QObject* _obj, QEvent* _event)
{
  // 处理属性窗口的事件
  if (_obj == property_view_.get())
  {
    // 捕获窗口大小变化和移动事件
    if (_event->type() == QEvent::Resize || _event->type() == QEvent::Move)
    {
      // 保存窗口几何信息
      savePropertyViewGeometry();
    }
    // 捕获窗口关闭事件
    else if (_event->type() == QEvent::Close)
    {
      // 保存窗口几何信息
      savePropertyViewGeometry();
      // 移除事件过滤器
      property_view_->removeEventFilter(this);
    }
  }

  return QtNodes::NodeDelegateModel::eventFilter(_obj, _event);
}

void BtNodeModel::updateNodeInfo(BT::Duration /*_timestamp*/,
                                 const std::string& /*_name*/,
                                 BT::NodeStatus /*_prev_status*/,
                                 BT::NodeStatus _status,
                                 const QVariantMap& _output_ports)
{
  if (!model_ptr_ || model_ptr_->registration_id == AbsBehaviorTree::getRootId())
  {
    return;
  }

  QString style_sheet;

  if (_status == BT::NodeStatus::RUNNING)
  {
    style_sheet = "background-color: #FFA500;"  // 橙色表示运行中
                  "color: white;"
                  "border-radius: 10px;";
  }
  else if (_status == BT::NodeStatus::SUCCESS)
  {
    style_sheet = "background-color: #7BB661;"  // 绿色表示成功
                  "color: white;"
                  "border-radius: 10px;";
  }
  else if (_status == BT::NodeStatus::FAILURE)
  {
    style_sheet = "background-color: #E74C3C;"  // 红色表示失败
                  "color: white;"
                  "border-radius: 10px;";
  }
  else
  {
    style_sheet = "background-color:rgb(129, 131, 129);"  // 默认灰色
                  "color: white;"
                  "border-radius: 10px;";
  }

  state_label_->setStyleSheet(style_sheet);

  // update output
  for (auto it = _output_ports.begin(); it != _output_ports.end(); ++it)
  {
    const QString& key    = it.key();
    const QVariant& value = it.value();

    LOG_DEBUG("key = {}", key);

    auto widget = ports_widgets_.find(key);
    if (widget == ports_widgets_.end())
    {
      continue;
    }

    auto* ptr = dynamic_cast<LineEditNode*>(widget->second);
    if (ptr == nullptr)
    {
      continue;
    }

    ptr->setText(value.toString());
  }
}

void BtNodeModel::showProperty(const QPoint& _show_pos)
{
  // 如果窗口已经可见，先隐藏再显示，确保重新激活
  if (property_view_->isVisible())
  {
    property_view_->hide();
  }

  // 从设置中恢复窗口大小和位置
  restorePropertyViewGeometry();

  // 如果没有保存的大小，则使用默认大小
  if (property_view_->size().isEmpty())
  {
    property_view_->adjustSize();
  }

  // 计算居中位置（如果没有保存的位置）
  if (!property_view_geometry_restored_)
  {
    QPoint centered_pos = _show_pos - QPoint(property_view_->width() / 2, property_view_->height() / 2);
    property_view_->move(centered_pos);
  }

  // 显示窗口并确保它获得焦点
  property_view_->show();
  property_view_->raise();
  property_view_->activateWindow();

  // 安装事件过滤器，用于捕获窗口大小变化
  property_view_->installEventFilter(this);
}

void BtNodeModel::savePropertyViewGeometry()
{
  if (!property_view_ || !model_ptr_)
  {
    return;
  }

  // 使用节点类型和ID作为唯一标识符
  QString settings_key = QString("NodePropertyView/%1_%2").arg(model_ptr_->registration_id).arg(uid_);

  QSettings settings("RoboSense", "FixtureTest");
  settings.setValue(settings_key + "/geometry", property_view_->saveGeometry());
  settings.setValue(settings_key + "/size", property_view_->size());
  settings.setValue(settings_key + "/pos", property_view_->pos());
}

void BtNodeModel::restorePropertyViewGeometry()
{
  if (!property_view_ || !model_ptr_)
  {
    property_view_geometry_restored_ = false;
    return;
  }

  // 使用节点类型和ID作为唯一标识符
  QString settings_key = QString("NodePropertyView/%1_%2").arg(model_ptr_->registration_id).arg(uid_);

  QSettings settings("RoboSense", "FixtureTest");

  // 恢复几何信息
  QByteArray geometry = settings.value(settings_key + "/geometry").toByteArray();
  QSize size          = settings.value(settings_key + "/size").toSize();
  QPoint pos          = settings.value(settings_key + "/pos").toPoint();

  bool restored = false;

  // 如果有保存的几何信息，则恢复
  if (!geometry.isEmpty())
  {
    restored = property_view_->restoreGeometry(geometry);
  }

  // 如果几何信息恢复失败，则尝试单独恢复大小和位置
  if (!restored)
  {
    if (size.isValid() && !size.isEmpty())
    {
      property_view_->resize(size);
      restored = true;
    }

    if (!pos.isNull())
    {
      property_view_->move(pos);
      restored = true;
    }
  }

  property_view_geometry_restored_ = restored;
}

PropertyModel* BtNodeModel::propertyModel() const
{
  if (!model_ptr_ || !model_ptr_->property_model)
  {
    return nullptr;
  }
  return model_ptr_->property_model.get();
}

void BtNodeModel::setInstanceName(const QString& _name)
{
  if (!model_ptr_)
  {
    return;
  }

  model_ptr_->setInstanceName(QStringView(_name));

  if (property_view_ && model_ptr_->property_model)
  {
    // 重新设置模型并刷新UI
    property_view_->setModel(model_ptr_->property_model);
  }

  updateNodeSize();
  emit instanceNameChanged();
}

void BtNodeModel::onHighlightPortValue(const QString& _value)
{
  for (const auto& iter : ports_widgets_)
  {
    auto* line_edit = dynamic_cast<QLineEdit*>(iter.second);
    if (line_edit == nullptr)
    {
      continue;
    }

    QString line_str = line_edit->text();
    QString style_sheet;

    if (!_value.isEmpty() && line_str == _value)
    {
      style_sheet = "color: rgb(30,30,30); "
                    "background-color: #ffef0b; "
                    "border: 0px; ";
    }
    else
    {
      style_sheet = "color: rgb(30,30,30); "
                    "background-color: rgb(200,200,200); "
                    "border: 0px; ";
    }

    line_edit->setStyleSheet(style_sheet);
  }
}

void LineEditNode::mouseDoubleClickEvent(QMouseEvent* /*_event*/) { emit doubleClicked(); }

void LineEditNode::focusOutEvent(QFocusEvent* _event)
{
  QLineEdit::focusOutEvent(_event);
  emit lostFocus();
}

}  // namespace robosense::lidar
