﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "abs_behavior_tree.h"
#include "property/property_model.h"
#include <behaviortree_cpp/decorators/subtree_node.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

NodeModel::NodeModel() : property_model(std::make_shared<PropertyModel>()) {}

// 使用QStringView的构造函数
NodeModel::NodeModel(BT::NodeType _type,
                     QStringView _registration_id,
                     QStringView _display_name,
                     QStringView _description) :
  type(_type),
  registration_id(_registration_id.toString()),
  display_name(_display_name.toString()),
  description(_description.toString()),
  property_model(std::make_shared<PropertyModel>())
{
  // 设置默认实例名称
  setInstanceName(_display_name);
}

NodeModel::NodeModel(BT::NodeType _type, QString&& _registration_id, QString&& _display_name, QString&& _description) :
  type(_type),
  registration_id(std::move(_registration_id)),
  display_name(std::move(_display_name)),
  description(std::move(_description)),
  property_model(std::make_shared<PropertyModel>())
{
  // 设置默认实例名称
  setInstanceName(this->display_name);
}

void NodeModel::initPropertyModel()
{
  if (!property_model)
  {
    property_model = std::make_shared<PropertyModel>();
  }

  // 注册节点基本信息到PropertyModel
  std::string node_type;
  switch (type)
  {
  case BT::NodeType::ACTION: node_type = "Action"; break;
  case BT::NodeType::CONDITION: node_type = "Condition"; break;
  case BT::NodeType::CONTROL: node_type = "Control"; break;
  case BT::NodeType::DECORATOR: node_type = "Decorator"; break;
  default: node_type = "Unknown"; break;
  }

  property_model->registerProperty<StringItem>("基本信息", "node_type", node_type);
  property_model->registerProperty<StringItem>("基本信息", "registration_id", registration_id.toStdString());

  // 注册实例名称（内部使用，用于BehaviorTree执行）
  property_model->registerProperty<StringItem>("唯一名称", "instance_name", instance_name.toStdString());

  // 注册显示名称（用户可编辑，在UI中显示）
  property_model->registerProperty<StringItem>("唯一名称", "display_name", instance_name.toStdString());

  // 注册端口信息
  for (const auto& port_it : ports)
  {
    const std::string& port_name = port_it.first;
    const PortModel& port        = port_it.second;

    std::string group_name;
    switch (port.direction)
    {
    case BT::PortDirection::INPUT: group_name = "Input Ports"; break;
    case BT::PortDirection::OUTPUT: group_name = "Output Ports"; break;
    default: group_name = "InOut Ports"; break;
    }

    // 根据端口数据类型注册不同类型的属性
    std::string key   = port.type_name.toStdString();
    std::string value = port.default_value.toStdString();

    // 设置端口描述
    if (!port.description.isEmpty())
    {
      property_model->setPropertyDescription(group_name, key, port.description.toStdString());
    }

    switch (port.data_type)
    {
    case PortDataType::INTEGER:
    {
      int default_value = port.default_value.toInt();
      int min_value     = static_cast<int>(port.min_value);
      int max_value     = static_cast<int>(port.max_value);
      int step          = static_cast<int>(port.step);
      property_model->registerProperty<IntItem>(group_name, key, default_value, min_value, max_value, step);
    }
    break;

    case PortDataType::DOUBLE:
    {
      double default_value = port.default_value.toDouble();
      property_model->registerProperty<DoubleItem>(group_name, key, default_value, port.min_value, port.max_value,
                                                   port.step, port.decimals);
    }
    break;

    case PortDataType::BOOLEAN:
    {
      bool default_value = (port.default_value.toLower() == "true");
      property_model->registerProperty<BoolItem>(group_name, key, default_value);
    }
    break;

    case PortDataType::ENUM:
    {
      if (!port.enum_options.isEmpty())
      {
        int default_index = 0;
        // 查找默认值在选项列表中的索引
        for (int i = 0; i < port.enum_options.size(); ++i)
        {
          if (port.enum_options[i] == port.default_value)
          {
            default_index = i;
            break;
          }
        }
        property_model->registerProperty<EnumItem>(group_name, key, port.enum_options, default_index);
      }
      else
      {
        // 如果没有枚举选项，退回到字符串类型，同样使用height_factor
        property_model->registerProperty<StringItem>(group_name, key, value, port.height_factor);
      }
    }
    break;

    case PortDataType::FILE_PATH:
    {
      property_model->registerProperty<FilePathPropertyItem>(group_name, key, value);
    }
    break;

    case PortDataType::DIRECTORY_PATH:
    {
      property_model->registerProperty<DirectoryPathPropertyItem>(group_name, key, value);
    }
    break;

    case PortDataType::STRING:
    default:
      // 使用端口的height_factor设置StringItem的高度，大于1时自动启用多行模式
      property_model->registerProperty<StringItem>(group_name, key, value, port.height_factor);
      break;
    }
  }
}

void NodeModel::setInstanceName(QStringView _name)
{
  // 同时更新NodeModel和PropertyModel
  instance_name = _name.toString();
  if (property_model)
  {
    property_model->setValue("唯一名称", "instance_name", instance_name.toStdString());
  }
}

void NodeModel::setInstanceName(const char* _name) { setInstanceName(QStringView(QString(_name))); }

void NodeModel::setPortValue(QStringView _port_name, QStringView _value)
{
  // 更新端口值
  bool port_found = false;
  std::string group_name;
  auto port_name     = _port_name.toString().toStdString();
  QString port_value = _value.toString();

  // 查找端口
  auto port_it = ports.find(port_name);
  if (port_it != ports.end())
  {
    PortModel updated_port_model = port_it->second;
    // 更新default_value
    updated_port_model.default_value = port_value;

    // 确定端口组名称
    switch (updated_port_model.direction)
    {
    case BT::PortDirection::INPUT: group_name = "Input Ports"; break;
    case BT::PortDirection::OUTPUT: group_name = "Output Ports"; break;
    default: group_name = "InOut Ports"; break;
    }

    // 使用erase和insert来更新ports中的值
    ports.erase(port_it);
    ports.insert({ port_name, std::move(updated_port_model) });

    port_found = true;
  }

  if (port_found && property_model)
  {
    // 重新获取更新后的端口
    auto updated_port_it = ports.find(port_name);
    if (updated_port_it != ports.end())
    {
      const PortModel& updated_port = updated_port_it->second;
      std::string key               = updated_port.type_name.toStdString();

      // 更新端口描述
      if (!updated_port.description.isEmpty())
      {
        property_model->setPropertyDescription(group_name, key, updated_port.description.toStdString());
      }

      // 根据端口数据类型设置不同类型的属性值
      switch (updated_port.data_type)
      {
      case PortDataType::INTEGER:
      {
        bool conversion_ok = false;
        int int_value      = port_value.toInt(&conversion_ok);
        if (conversion_ok)
        {
          property_model->setValue(group_name, key, int_value);
        }
        else
        {
          // 转换失败，使用默认值
          property_model->setValue(group_name, key, 0);
        }
      }
      break;

      case PortDataType::DOUBLE:
      {
        bool conversion_ok  = false;
        double double_value = port_value.toDouble(&conversion_ok);
        if (conversion_ok)
        {
          property_model->setValue(group_name, key, double_value);
        }
        else
        {
          // 转换失败，使用默认值
          property_model->setValue(group_name, key, 0.0);
        }
      }
      break;

      case PortDataType::BOOLEAN:
      {
        QString lower_value = port_value.toLower();
        bool bool_value = (lower_value == "true" || lower_value == "1" || lower_value == "yes" || lower_value == "on");
        property_model->setValue(group_name, key, bool_value);
      }
      break;

      case PortDataType::ENUM:
      {
        if (!updated_port.enum_options.isEmpty())
        {
          int index = 0;
          // 查找值在选项列表中的索引
          for (int i = 0; i < updated_port.enum_options.size(); ++i)
          {
            if (updated_port.enum_options[i] == port_value)
            {
              index = i;
              break;
            }
          }
          property_model->setValue(group_name, key, index);
        }
        else
        {
          // 如果没有枚举选项，退回到字符串类型
          property_model->setValue(group_name, key, port_value.toStdString());
        }
      }
      break;

      case PortDataType::FILE_PATH:
      case PortDataType::DIRECTORY_PATH:
      {
        property_model->setValue(group_name, key, port_value.toStdString());
      }
      break;

      case PortDataType::STRING:
      default: property_model->setValue(group_name, key, port_value.toStdString()); break;
      }
    }
  }
}

void NodeModel::setPortValue(const char* _port_name, const char* _value)
{
  setPortValue(QStringView(QString(_port_name)), QStringView(QString(_value)));
}

QString NodeModel::getPortValue(QStringView _port_name) const
{
  std::string port_name = _port_name.toString().toStdString();
  for (const auto& port_it : ports)
  {
    if (port_it.first == port_name)
    {
      // 直接返回存储的默认值
      return port_it.second.default_value;
    }
  }
  return {};
}

QString NodeModel::getPortValue(const char* _port_name) const { return getPortValue(QStringView(QString(_port_name))); }

NodeInfo NodeModel::getNodeInfo(std::string_view _category) const
{
  return { registration_id.toStdString(), display_name.toStdString(), description.toStdString(),
           std::string(_category) };
}

NodeInfo NodeModel::getNodeInfo(const char* _category) const { return getNodeInfo(std::string_view(_category)); }

void AbsBehaviorTree::clear() { nodes_.resize(0); }

AbsBehaviorTree::~AbsBehaviorTree() { clear(); }

AbstractTreeNode* AbsBehaviorTree::rootNode()
{
  if (nodes_.empty())
  {
    return nullptr;
  }
  return &nodes_.front();
}

const AbstractTreeNode* AbsBehaviorTree::rootNode() const
{
  if (nodes_.empty())
  {
    return nullptr;
  }
  return &nodes_.front();
}

std::vector<const AbstractTreeNode*> AbsBehaviorTree::findNodes(const QString& _instance_name)
{
  std::vector<const AbstractTreeNode*> out;
  out.reserve(4);

  for (const auto& node : nodes_)
  {
    if (node.instance_name == _instance_name)
    {
      out.push_back(&node);
    }
  }
  return out;
}

const AbstractTreeNode* AbsBehaviorTree::findFirstNode(const QString& _instance_name)
{
  for (const auto& node : nodes_)
  {
    if (node.instance_name == _instance_name)
    {
      return (&node);
    }
  }
  return nullptr;
}

AbstractTreeNode* AbsBehaviorTree::addNode(AbstractTreeNode* _parent, AbstractTreeNode&& _new_node)
{
  int index       = static_cast<int>(nodes_.size());
  _new_node.index = index;
  if (_parent != nullptr)
  {
    nodes_.push_back(std::move(_new_node));
    _parent->children_index.push_back(index);
  }
  else
  {
    nodes_.clear();
    nodes_.push_back(_new_node);
  }
  return &nodes_.back();
}

void AbsBehaviorTree::debugPrint() const
{
  if (rootNode() == nullptr)
  {
    return;
  }
  std::function<void(const AbstractTreeNode*, int)> recursive_step;

  recursive_step = [&](const AbstractTreeNode* _node, int _indent) {
    for (int i = 0; i < _indent; i++)
    {
      LOG_DEBUG("    ");
    }

    LOG_DEBUG("{} ({})", _node->instance_name, _node->model->registration_id);

    for (int index : _node->children_index)
    {
      const auto* child_node = &nodes_[index];
      recursive_step(child_node, _indent + 1);
    }
  };

  recursive_step(rootNode(), 0);
}

bool AbsBehaviorTree::operator==(const AbsBehaviorTree& _other) const
{
  if (nodes_.size() != _other.nodes_.size())
  {
    return false;
  }

  for (size_t index = 0; index < nodes_.size(); index++)
  {
    if (nodes_[index] != _other.nodes_[index])
    {
      return false;
    }
  }
  return true;
}

int AbsBehaviorTree::getUID()
{
  static int uid = 1000;
  return uid++;
}

const QString& AbsBehaviorTree::getRootId()
{
  static const QString G_ROOT_ID("开始");
  return G_ROOT_ID;
}

GraphicMode getGraphicModeFromString(const QString& _str)
{
  if (_str == "EDITOR")
  {
    return GraphicMode::EDITOR;
  }
  if (_str == "MONITOR")
  {
    return GraphicMode::MONITOR;
  }
  return GraphicMode::REPLAY;
}

const char* toStr(GraphicMode _type)
{
  if (_type == GraphicMode::EDITOR)
  {
    return "EDITOR";
  }
  if (_type == GraphicMode::MONITOR)
  {
    return "MONITOR";
  }
  if (_type == GraphicMode::REPLAY)
  {
    return "REPLAY";
  }
  return nullptr;
}

bool AbstractTreeNode::operator==(const AbstractTreeNode& _other) const
{
  if (!model || !_other.model)
  {
    return false;
  }
  bool same_registration = model->registration_id == _other.model->registration_id;
  return same_registration && status == _other.status && size == _other.size &&
         // temporary removed  pos == _other.pos &&
         instance_name == _other.instance_name;
}

bool NodeModel::operator==(const NodeModel& _other) const
{
  bool is_same =
    (type == _other.type && ports.size() == _other.ports.size() && registration_id == _other.registration_id);
  if (!is_same)
  {
    return false;
  }

  auto other_it = _other.ports.begin();
  for (const auto& port_it : ports)
  {
    if (port_it.first != other_it->first)
    {
      return false;
    }
    if (port_it.second.direction != other_it->second.direction)
    {
      return false;
    }
    if (port_it.second.type_name != other_it->second.type_name)
    {
      return false;
    }
    other_it++;
  }
  return true;
}

NodeModel& NodeModel::operator=(const BT::TreeNodeManifest& _src)
{
  this->type            = _src.type;
  this->registration_id = QString::fromStdString(_src.registration_ID);
  for (const auto& port_it : _src.ports)
  {
    const auto& port_name = port_it.first;
    const auto& bt_port   = port_it.second;
    PortModel port_model;
    port_model = bt_port;
    this->ports.insert({ port_name, std::move(port_model) });
  }
  return *this;
}

const NodeModels& builtinNodeModels()
{
  static NodeModels builtin_node_models = []() -> NodeModels {
    NodeModels out;

    // Root节点
    {
      PortModels port_models;
      auto root_model             = std::make_shared<NodeModel>();
      root_model->type            = BT::NodeType::SUBTREE;
      root_model->registration_id = AbsBehaviorTree::getRootId();
      root_model->ports           = port_models;
      root_model->setInstanceName(QStringView(u"Root"));
      out.try_emplace(AbsBehaviorTree::getRootId().toStdString(), root_model);
    }

    // SetBlackboard节点
    {
      auto set_blackboard_model             = std::make_shared<NodeModel>();
      set_blackboard_model->type            = BT::NodeType::ACTION;
      set_blackboard_model->registration_id = "SetBlackboard";
      set_blackboard_model->display_name    = "设置黑板变量";
      set_blackboard_model->description     = "将值存储到黑板的指定键中";
      set_blackboard_model->setInstanceName("SetBlackboard");

      PortModels ports;

      // value输入端口
      PortModel value_port;
      value_port.type_name     = "value";
      value_port.direction     = BT::PortDirection::INPUT;
      value_port.description   = "要写入的值";
      value_port.default_value = "";
      value_port.data_type     = PortDataType::STRING;
      ports.insert({ "值", std::move(value_port) });

      // output_key输入端口
      PortModel output_key_port;
      output_key_port.type_name     = "output_key";
      output_key_port.direction     = BT::PortDirection::INPUT;
      output_key_port.description   = "黑板键名，值将被写入此键";
      output_key_port.default_value = "";
      output_key_port.data_type     = PortDataType::STRING;
      ports.insert({ "输出键", std::move(output_key_port) });

      set_blackboard_model->ports = std::move(ports);
      out.try_emplace("SetBlackboard", set_blackboard_model);
    }

    // UnsetBlackboard节点
    {
      auto unset_blackboard_model             = std::make_shared<NodeModel>();
      unset_blackboard_model->type            = BT::NodeType::ACTION;
      unset_blackboard_model->registration_id = "UnsetBlackboard";
      unset_blackboard_model->display_name    = "删除黑板变量";
      unset_blackboard_model->description     = "从黑板中删除指定的键";
      unset_blackboard_model->setInstanceName("UnsetBlackboard");

      PortModels ports;

      // key输入端口
      PortModel key_port;
      key_port.type_name     = "key";
      key_port.direction     = BT::PortDirection::INPUT;
      key_port.description   = "要删除的黑板键名";
      key_port.default_value = "";
      key_port.data_type     = PortDataType::STRING;
      ports.insert({ "键", std::move(key_port) });

      unset_blackboard_model->ports = std::move(ports);
      out.try_emplace("UnsetBlackboard", unset_blackboard_model);
    }

    // PrintVariable节点
    {
      auto print_variable_model             = std::make_shared<NodeModel>();
      print_variable_model->type            = BT::NodeType::ACTION;
      print_variable_model->registration_id = "PrintVariable";
      print_variable_model->display_name    = "打印变量";
      print_variable_model->description     = "根据输入变量打印其结果到控制台";
      print_variable_model->setInstanceName("PrintVariable");

      PortModels ports;

      // variable输入端口
      PortModel variable_port;
      variable_port.type_name     = "variable";
      variable_port.direction     = BT::PortDirection::INPUT;
      variable_port.description   = "要打印的变量值";
      variable_port.default_value = "";
      variable_port.data_type     = PortDataType::STRING;
      ports.insert({ "变量值", std::move(variable_port) });

      // variable_name输入端口（可选）
      PortModel variable_name_port;
      variable_name_port.type_name     = "variable_name";
      variable_name_port.direction     = BT::PortDirection::INPUT;
      variable_name_port.description   = "变量名称（可选，默认为'变量'）";
      variable_name_port.default_value = "变量";
      variable_name_port.data_type     = PortDataType::STRING;
      ports.insert({ "变量名称", std::move(variable_name_port) });

      print_variable_model->ports = std::move(ports);
      out.try_emplace("PrintVariable", print_variable_model);
    }

    return out;
  }();

  return builtin_node_models;
}

PortModel& PortModel::operator=(const BT::PortInfo& _src)
{
  this->direction     = _src.direction();
  this->description   = QString::fromStdString(_src.description());
  this->type_name     = QString::fromStdString(BT::demangle(_src.type()));
  this->default_value = QString::fromStdString(_src.defaultValue().cast<std::string>());

  // 根据类型名称推断数据类型
  this->data_type = inferPortDataType(this->type_name);

  // 根据数据类型设置默认值
  if (this->data_type == PortDataType::BOOLEAN)
  {
    // 确保布尔值是 "true" 或 "false"
    QString lower_value = this->default_value.toLower();
    if (lower_value == "1" || lower_value == "true" || lower_value == "yes" || lower_value == "on")
    {
      this->default_value = "true";
    }
    else
    {
      this->default_value = "false";
    }
  }
  else if (this->data_type == PortDataType::INTEGER || this->data_type == PortDataType::DOUBLE)
  {
    // 确保数值是有效的
    bool conversion_success = false;
    if (this->data_type == PortDataType::INTEGER)
    {
      this->default_value.toInt(&conversion_success);
      if (!conversion_success)
      {
        this->default_value = "0";
      }
      this->step     = 1.0;
      this->decimals = 0;
    }
    else
    {
      this->default_value.toDouble(&conversion_success);
      if (!conversion_success)
      {
        this->default_value = "0.0";
      }
      this->step     = 0.1;
      this->decimals = 2;
    }
  }

  return *this;
}

}  // namespace robosense::lidar
