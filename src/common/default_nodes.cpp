/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the BSD 3-Clause License.
 * You may obtain a copy of the License at:
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its contributors
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "default_nodes.h"
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

PrintVariableNode::PrintVariableNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config), node_name_(_name)
{}

BT::NodeStatus PrintVariableNode::tick()
{
  // 使用卫语句避免嵌套
  auto input_result = getInput<std::string>("variable");
  if (!input_result)
  {
    LOG_ERROR("[{}] 无法读取输入端口 'variable': {}", node_name_, input_result.error());
    return BT::NodeStatus::FAILURE;
  }

  const std::string& variable_value = input_result.value();

  // 获取可选的变量名称，如果没有提供则使用默认值
  auto variable_name_result = getInput<std::string>("variable_name");
  std::string variable_name = variable_name_result ? variable_name_result.value() : "变量";

  // 打印变量信息
  LOG_INFO("[{}] {}: {}", node_name_, variable_name, variable_value);

  return BT::NodeStatus::SUCCESS;
}

BT::PortsList PrintVariableNode::providedPorts()
{
  return { BT::InputPort<std::string>("variable", "要打印的变量值"),
           BT::InputPort<std::string>("variable_name", "变量名称（可选，默认为'变量'）") };
}

void registerDefaultNodes(BT::BehaviorTreeFactory& _factory)
{
  _factory.registerNodeType<PrintVariableNode>("PrintVariable");
}

}  // namespace robosense::lidar
