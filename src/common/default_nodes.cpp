/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the BSD 3-Clause License.
 * You may obtain a copy of the License at:
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its contributors
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "default_nodes.h"
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

PrintVariableNode::PrintVariableNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config), node_name_(_name)
{}

BT::NodeStatus PrintVariableNode::tick()
{
  // 使用卫语句避免嵌套 - 获取字符串输入
  auto input_result = getInput<std::string>("variable");
  if (!input_result)
  {
    LOG_ERROR("[{}] 无法读取输入端口 'variable': {}", node_name_, input_result.error());
    return BT::NodeStatus::FAILURE;
  }

  const std::string& input_string = input_result.value();

  // 获取可选的变量名称，如果没有提供则使用默认值
  auto variable_name_result = getInput<std::string>("variable_name");
  std::string variable_name = variable_name_result ? variable_name_result.value() : "变量";

  std::string value_string;

  // 检查是否是黑板引用（以{开头和}结尾）
  if (input_string.size() >= 3 && input_string.front() == '{' && input_string.back() == '}')
  {
    // 这是一个黑板引用，尝试获取实际的Any值
    std::string blackboard_key = input_string.substr(1, input_string.size() - 2);

    if (config().blackboard)
    {
      if (auto entry = config().blackboard->getEntry(blackboard_key))
      {
        std::unique_lock lock(entry->entry_mutex);
        value_string = convertAnyToString(entry->value);
      }
      else
      {
        value_string = "<blackboard key not found: " + blackboard_key + ">";
      }
    }
    else
    {
      value_string = "<no blackboard available>";
    }
  }
  else
  {
    // 这是一个直接的字符串值
    value_string = input_string;
  }

  // 打印变量信息
  LOG_INFO("[{}] {}: {}", node_name_, variable_name, value_string);

  return BT::NodeStatus::SUCCESS;
}

BT::PortsList PrintVariableNode::providedPorts()
{
  return { BT::InputPort<std::string>("variable", "要打印的变量值（支持所有类型）"),
           BT::InputPort<std::string>("variable_name", "变量名称（可选，默认为'变量'）") };
}

std::string PrintVariableNode::convertAnyToString(const BT::Any& _value)
{
  // 如果值为空，返回空字符串
  if (_value.empty())
  {
    return "<empty>";
  }

  try
  {
    // 字符串类型 - 优先处理
    if (_value.isString())
    {
      return _value.cast<std::string>();
    }

    // 检查是否是数值类型
    if (_value.isNumber())
    {
      // 先尝试浮点数（因为浮点数可以表示整数，但整数不能表示小数）
      if (auto double_result = _value.tryCast<double>())
      {
        double val = double_result.value();
        // 检查是否是整数值
        if (val == std::floor(val))
        {
          return std::to_string(static_cast<int64_t>(val));
        }

        return std::to_string(val);
      }

      if (auto float_result = _value.tryCast<float>())
      {
        float val = float_result.value();
        // 检查是否是整数值
        if (val == std::floor(val))
        {
          return std::to_string(static_cast<int64_t>(val));
        }

        return std::to_string(val);
      }

      // 尝试整数类型
      if (auto int64_result = _value.tryCast<int64_t>())
      {
        return std::to_string(int64_result.value());
      }

      // 尝试无符号整数
      if (auto uint64_result = _value.tryCast<uint64_t>())
      {
        return std::to_string(uint64_result.value());
      }
    }

    // 布尔类型 - 在数值类型之后检查，避免与数值混淆
    // 使用更安全的方式检查布尔类型，避免字符串到布尔的隐式转换
    try
    {
      if (auto bool_result = _value.tryCast<bool>())
      {
        return bool_result.value() ? "true" : "false";
      }
    }
    catch (const std::exception&)
    {
      // 如果布尔转换失败，继续尝试其他方法
    }

    // 尝试使用BT的toJsonString功能
    if (auto json_result = BT::toJsonString(_value))
    {
      return json_result.value();
    }

    // 如果所有转换都失败，返回类型信息
    return "<unknown type>";
  }
  catch (const std::exception& exception)
  {
    return std::string("<conversion error: ") + exception.what() + ">";
  }
}

void registerDefaultNodes(BT::BehaviorTreeFactory& _factory)
{
  _factory.registerNodeType<PrintVariableNode>("PrintVariable");
}

}  // namespace robosense::lidar
