/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the BSD 3-Clause License.
 * You may obtain a copy of the License at:
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its contributors
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "default_nodes.h"
#include <behaviortree_cpp/bt_factory.h>
#include <iostream>

/**
 * @brief 测试PrintVariable节点的基本功能
 */
void testPrintVariableNode()
{
  std::cout << "=== 测试PrintVariable节点 ===" << std::endl;

  // 创建BehaviorTree工厂
  BT::BehaviorTreeFactory factory;

  // 注册默认节点
  robosense::lidar::registerDefaultNodes(factory);

  // 创建XML定义 - 测试各种数据类型
  const char* xml_text = R"(
    <root BTCPP_format="4">
      <BehaviorTree ID="TestTree">
        <Sequence>
          <Script code="string_value:='Hello World'" />
          <PrintVariable variable="{string_value}" variable_name="字符串变量" />

          <Script code="int_value:=42" />
          <PrintVariable variable="{int_value}" variable_name="整数变量" />

          <Script code="float_value:=3.14159" />
          <PrintVariable variable="{float_value}" variable_name="浮点数变量" />

          <Script code="bool_value:=true" />
          <PrintVariable variable="{bool_value}" variable_name="布尔变量" />

          <Script code="negative_int:=-123" />
          <PrintVariable variable="{negative_int}" variable_name="负整数" />

          <PrintVariable variable="直接字符串值" variable_name="直接值" />

          <PrintVariable variable="123" variable_name="直接数字字符串" />
        </Sequence>
      </BehaviorTree>
    </root>
  )";

  try
  {
    // 从XML创建行为树
    auto tree = factory.createTreeFromText(xml_text);

    std::cout << "开始执行行为树..." << std::endl;

    // 执行行为树
    BT::NodeStatus status = tree.tickWhileRunning();

    if (status == BT::NodeStatus::SUCCESS)
    {
      std::cout << "✓ 行为树执行成功！" << std::endl;
    }
    else
    {
      std::cout << "✗ 行为树执行失败，状态: " << static_cast<int>(status) << std::endl;
    }
  }
  catch (const std::exception& exception)
  {
    std::cout << "✗ 异常: " << exception.what() << std::endl;
  }

  std::cout << "=== 测试完成 ===" << std::endl;
}

int main()
{
  testPrintVariableNode();
  return 0;
}
